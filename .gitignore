# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# production
/build

# misc
.DS_Store
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Environment variables (keep .env.example for reference)
.env.*
!.env.example
!.env.production

npm-debug.log*
yarn-debug.log*
yarn-error.log*

commit.sh
