# Configuration des Variables d'Environnement - UIZ Diplome

## 📋 Vue d'ensemble

Ce projet utilise des variables d'environnement pour gérer la configuration de l'application de manière flexible et sécurisée. Toutes les variables sont centralisées dans des fichiers `.env` et accessibles via le module `src/config/environment.js`.

## 🗂️ Fichiers de Configuration

### `.env`
Fichier principal pour le développement local. **Ne pas commiter ce fichier !**

### `.env.example`
Template de configuration avec des valeurs d'exemple. À utiliser comme référence.

### `.env.production`
Configuration pour l'environnement de production.

### `src/config/environment.js`
Module JavaScript qui centralise l'accès aux variables d'environnement.

## 🚀 Installation et Configuration

### 1. Configuration Initiale

```bash
# Copier le fichier d'exemple
cp .env.example .env

# Éditer le fichier .env avec vos valeurs
nano .env
```

### 2. Variables Essentielles à Configurer

#### API Configuration
```env
REACT_APP_API_BASE_URL=http://localhost:8000/api
REACT_APP_API_VERSION=v1
```

#### Google Maps (Optionnel)
```env
REACT_APP_GOOGLE_MAPS_API_KEY=your_actual_google_maps_api_key
```

#### Configuration Keycloak (Recommandé)
```env
REACT_APP_KEYCLOAK_URL=http://localhost:8080
REACT_APP_KEYCLOAK_REALM=uiz-diplome
REACT_APP_KEYCLOAK_CLIENT_ID=uiz-diplome-frontend
REACT_APP_KEYCLOAK_PUBLIC_CLIENT=true
```

#### Configuration JWT
```env
REACT_APP_JWT_SECRET_KEY=your-secret-key-change-in-production
REACT_APP_JWT_EXPIRES_IN=24h
REACT_APP_JWT_REFRESH_EXPIRES_IN=7d
```

#### Authentification OAuth (Optionnel)
```env
REACT_APP_GITHUB_CLIENT_ID=your_github_client_id
REACT_APP_GOOGLE_CLIENT_ID=your_google_client_id
```

## 📚 Utilisation dans le Code

### Import du Module de Configuration

```javascript
import config from 'config/environment';

// Utilisation
const apiUrl = config.api.baseUrl;
const appName = config.app.name;
const isProduction = config.app.isProduction;
```

### Exemples d'Utilisation

#### Dans un composant React
```javascript
import React from 'react';
import config from 'config/environment';

function MyComponent() {
  const handleApiCall = async () => {
    const response = await fetch(`${config.api.baseUrl}/users`);
    // ...
  };

  return (
    <div>
      <h1>{config.app.title}</h1>
      {/* ... */}
    </div>
  );
}
```

#### Pour la configuration de Google Maps
```javascript
import config from 'config/environment';

const mapOptions = {
  center: {
    lat: config.map.defaultLat,
    lng: config.map.defaultLng
  },
  zoom: config.map.defaultZoom
};
```

## 🔧 Variables Disponibles

### Application
- `REACT_APP_NAME` - Nom de l'application
- `REACT_APP_VERSION` - Version de l'application
- `REACT_APP_TITLE` - Titre affiché
- `REACT_APP_ENVIRONMENT` - Environnement (development/production)

### API
- `REACT_APP_API_BASE_URL` - URL de base de l'API
- `REACT_APP_API_VERSION` - Version de l'API
- `REACT_APP_API_TIMEOUT` - Timeout des requêtes (ms)

### Services Externes
- `REACT_APP_GOOGLE_MAPS_API_KEY` - Clé API Google Maps
- `REACT_APP_GITHUB_CLIENT_ID` - ID client GitHub OAuth
- `REACT_APP_GOOGLE_CLIENT_ID` - ID client Google OAuth

### Carte
- `REACT_APP_MAP_DEFAULT_LAT` - Latitude par défaut
- `REACT_APP_MAP_DEFAULT_LNG` - Longitude par défaut
- `REACT_APP_MAP_DEFAULT_ZOOM` - Zoom par défaut

### Thème et Couleurs
- `REACT_APP_PRIMARY_COLOR` - Couleur primaire
- `REACT_APP_SECONDARY_COLOR` - Couleur secondaire
- `REACT_APP_BACKGROUND_COLOR` - Couleur de fond

## 🛠️ Fonctions Utilitaires

### Validation de la Configuration
```javascript
import { validateConfig } from 'config/environment';

// Valider la configuration au démarrage
validateConfig();
```

### Logging de la Configuration
```javascript
import { logConfig } from 'config/environment';

// Afficher la configuration en mode debug
logConfig();
```

## 🔒 Sécurité

### Variables Sensibles
- **Ne jamais commiter** les fichiers `.env` contenant des clés API réelles
- Utiliser des variables d'environnement du serveur pour les secrets en production
- Préfixer toutes les variables client avec `REACT_APP_`

### Bonnes Pratiques
1. Utiliser `.env.example` pour documenter les variables requises
2. Valider la configuration au démarrage de l'application
3. Utiliser des valeurs par défaut sécurisées
4. Séparer les configurations par environnement

## 🌍 Environnements

### Développement
```bash
npm start
# Utilise .env par défaut
```

### Production
```bash
npm run build
# Utilise .env.production si disponible
```

### Test
```bash
npm test
# Utilise .env.test si disponible
```

## 🐛 Dépannage

### Variables Non Reconnues
- Vérifier que la variable commence par `REACT_APP_`
- Redémarrer le serveur de développement après modification
- Vérifier l'orthographe de la variable

### Valeurs Non Mises à Jour
- Redémarrer le serveur de développement
- Vider le cache du navigateur
- Vérifier que le fichier `.env` est dans le bon répertoire

### Erreurs de Configuration
```javascript
// Activer le mode debug pour plus d'informations
REACT_APP_DEBUG_MODE=true
REACT_APP_ENABLE_CONSOLE_LOGS=true
```

## 📝 Notes Importantes

1. **Redémarrage Requis** : Redémarrez le serveur après modification des variables
2. **Ordre de Priorité** : `.env.local` > `.env.production` > `.env` > `.env.example`
3. **Variables Client** : Seules les variables `REACT_APP_*` sont accessibles côté client
4. **Sécurité** : Ne jamais exposer de secrets dans les variables `REACT_APP_*`

## 🤝 Contribution

Lors de l'ajout de nouvelles variables :
1. Ajouter la variable dans `.env.example`
2. Documenter son utilisation dans ce README
3. Ajouter la variable dans `src/config/environment.js`
4. Mettre à jour `.env.production` si nécessaire
